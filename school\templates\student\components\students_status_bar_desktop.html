<!-- Desktop Students Status Bar Component - Inline in header -->
<div class="students-status-bar desktop-status-bar" id="students-status-bar-desktop">
    <div class="status-info">
        <span class="status-item">
            <span class="material-icons">group</span>
            <span id="total-count-desktop">{{ result_boys|add:result_girls }}</span> élèves
        </span>
        <span class="status-separator">•</span>
        <span class="status-item">
            <span class="material-icons">male</span>
            <span id="boys-count-desktop">{{ result_boys }}</span> garçons
        </span>
        <span class="status-separator">•</span>
        <span class="status-item">
            <span class="material-icons">female</span>
            <span id="girls-count-desktop">{{ result_girls }}</span> filles
        </span>
    </div>
    <div class="status-filters" id="status-filters-desktop">
        <!-- Sort indicator -->
        <span class="status-filter" id="sort-indicator-desktop" style="display: none;">
            <span class="material-icons">sort</span>
            <span id="sort-text-desktop"></span>
        </span>
        <!-- Filter indicator -->
        <span class="status-filter" id="filter-indicator-desktop" style="display: none;">
            <span class="material-icons">filter_list</span>
            <span id="filter-text-desktop"></span>
        </span>
        <!-- Search indicator -->
        <span class="status-filter" id="search-indicator-desktop" style="display: none;">
            <span class="material-icons">search</span>
            <span id="search-text-desktop"></span>
        </span>
    </div>
</div>

<style>
/* Desktop Students Status Bar Styles - Inline in header */
.desktop-status-bar {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 13px;
    color: var(--text-secondary, #666);
    margin: 0 16px;
    flex: 1;
    justify-content: center;
}

.desktop-status-bar .status-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.desktop-status-bar .status-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.desktop-status-bar .status-item .material-icons {
    font-size: 16px;
    color: var(--text-secondary, #666);
}

.desktop-status-bar .status-separator {
    color: var(--text-disabled, #999);
    font-weight: bold;
}

.desktop-status-bar .status-filters {
    display: flex;
    align-items: center;
    gap: 8px;
}

.desktop-status-bar .status-filter {
    display: flex;
    align-items: center;
    gap: 4px;
    background: var(--primary-light, #e8f5e8);
    color: var(--primary, #4caf50);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.desktop-status-bar .status-filter .material-icons {
    font-size: 14px;
}

/* Hide desktop status bar on mobile */
@media (max-width: 768px) {
    .desktop-status-bar {
        display: none;
    }
}

/* Responsive adjustments for smaller desktop screens */
@media (min-width: 769px) and (max-width: 1200px) {
    .desktop-status-bar {
        font-size: 12px;
        gap: 12px;
    }
    
    .desktop-status-bar .status-info {
        gap: 10px;
    }
    
    .desktop-status-bar .status-item .material-icons {
        font-size: 14px;
    }
}
</style>

<script>
// Desktop Students Status Bar JavaScript Component
(function() {
    'use strict';
    
    // Update desktop status bar with current filters and sort
    function updateStudentsStatusBarDesktop() {
        const sortIndicator = document.getElementById('sort-indicator-desktop');
        const sortText = document.getElementById('sort-text-desktop');
        const filterIndicator = document.getElementById('filter-indicator-desktop');
        const filterText = document.getElementById('filter-text-desktop');
        const searchIndicator = document.getElementById('search-indicator-desktop');
        const searchText = document.getElementById('search-text-desktop');

        // Update sort indicator
        const urlParams = new URLSearchParams(window.location.search);
        const currentSort = urlParams.get('sort');
        if (currentSort && sortIndicator && sortText) {
            const sortLabels = {
                'student__last_name': 'Nom (A-Z)',
                '-student__last_name': 'Nom (Z-A)',
                'student__first_name': 'Prénom (A-Z)',
                '-student__first_name': 'Prénom (Z-A)',
                'student__matricule': 'Matricule (croissant)',
                '-student__matricule': 'Matricule (décroissant)',
                '-created_at': 'Plus récents',
                'created_at': 'Plus anciens'
            };
            
            if (sortLabels[currentSort]) {
                sortText.textContent = sortLabels[currentSort];
                sortIndicator.style.display = 'flex';
            } else {
                sortIndicator.style.display = 'none';
            }
        } else if (sortIndicator) {
            sortIndicator.style.display = 'none';
        }

        // Update filter indicator
        const currentFilter = urlParams.get('filter_by');
        if (currentFilter && currentFilter !== 'all' && filterIndicator && filterText) {
            const filterLabels = {
                'paid': 'Payé',
                'unpaid': 'Non payé',
                'full_paid': 'Soldé',
                'today': 'Aujourd\'hui',
                'partial_paid': 'Partiellement payé'
            };
            
            if (filterLabels[currentFilter]) {
                filterText.textContent = filterLabels[currentFilter];
                filterIndicator.style.display = 'flex';
            } else {
                filterIndicator.style.display = 'none';
            }
        } else if (filterIndicator) {
            filterIndicator.style.display = 'none';
        }

        // Update search indicator
        const searchQuery = urlParams.get('search');
        if (searchQuery && searchIndicator && searchText) {
            searchText.textContent = `"${searchQuery}"`;
            searchIndicator.style.display = 'flex';
        } else if (searchIndicator) {
            searchIndicator.style.display = 'none';
        }
    }

    // Update counts in desktop status bar
    function updateStudentsStatusCountsDesktop(boysCount, girlsCount) {
        const totalCountEl = document.getElementById('total-count-desktop');
        const boysCountEl = document.getElementById('boys-count-desktop');
        const girlsCountEl = document.getElementById('girls-count-desktop');
        
        if (totalCountEl) totalCountEl.textContent = boysCount + girlsCount;
        if (boysCountEl) boysCountEl.textContent = boysCount;
        if (girlsCountEl) girlsCountEl.textContent = girlsCount;
    }

    // Make functions globally available
    window.updateStudentsStatusBarDesktop = updateStudentsStatusBarDesktop;
    window.updateStudentsStatusCountsDesktop = updateStudentsStatusCountsDesktop;

    // Initialize status bar when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', updateStudentsStatusBarDesktop);
    } else {
        updateStudentsStatusBarDesktop();
    }

    // Update status bar after HTMX swaps
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'content-area' || 
            event.detail.target.querySelector && event.detail.target.querySelector('#students-status-bar-desktop')) {
            setTimeout(() => updateStudentsStatusBarDesktop(), 100);
        }
    });
})();
</script>
