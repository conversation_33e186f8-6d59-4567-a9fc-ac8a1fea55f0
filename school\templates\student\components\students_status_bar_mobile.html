<!-- Mobile Students Status Bar Component -->
<div class="students-status-bar mobile-status-bar" id="students-status-bar">
    <div class="status-info">
        <span class="status-item">
            <span class="material-icons">group</span>
            <span id="total-count">{{ result_boys|add:result_girls }}</span>
        </span>
        <span class="status-separator">•</span>
        <span class="status-item">
            <span class="material-icons">boy</span>
            <span id="boys-count">{{ result_boys }}</span>
        </span>
        <span class="status-separator">•</span>
        <span class="status-item">
            <span class="material-icons">girl</span>
            <span id="girls-count">{{ result_girls }}</span>
        </span>
        <!-- Sort indicator inline with counts - always visible -->
        <span class="status-separator">•</span>
        <span class="status-sort-indicator" id="sort-indicator" onclick="showSortBottomSheet()">
            <span class="material-icons">sort</span>
            <span id="sort-text">Trier</span>
        </span>
    </div>
</div>

<style>
/* Remove margin from content header to eliminate spacing with status bar - only on mobile templates with status bar */
.mobile-with-status-bar .content-header {
    margin-bottom: 0 !important;
}

/* Mobile Students Status Bar Styles */
.mobile-status-bar {
    z-index: 10;
    background: var(--surface, #fff);
    border-bottom: 1px solid var(--divider, #e0e0e0);
    padding: 8px 16px;
    margin: 0; /* Remove any margin */
    font-size: 12px;
    color: var(--text-secondary, #666);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.mobile-status-bar .status-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.mobile-status-bar .status-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.mobile-status-bar .status-item .material-icons {
    font-size: 20px;
    color: var(--text-secondary, #666);
}

.mobile-status-bar .status-separator {
    color: var(--text-disabled, #999);
    font-weight: bold;
}

.mobile-status-bar .status-sort-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    background: var(--background-secondary, #e8f5e8);
    color: var(--text-primary, #4caf50);
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.mobile-status-bar .status-sort-indicator:hover {
    background: var(--primary-light, #d4edda);
}

.mobile-status-bar .status-sort-indicator .material-icons {
    font-size: 12px;
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .mobile-status-bar .status-info {
        gap: 6px;
    }
}

/* Hide mobile status bar on desktop */
@media (min-width: 769px) {
    .mobile-status-bar {
        display: none;
    }
}
</style>

<script>
// Mobile Students Status Bar JavaScript Component
(function() {
    'use strict';
    
    // Update status bar with current sort indicator - always visible
    function updateStudentsStatusBar() {
        const sortText = document.getElementById('sort-text');

        // Update sort indicator text based on current sort
        const currentSort = window.studentsListInfiniteScroll?.currentSort;
        if (currentSort && sortText) {
            const sortLabels = {
                'student__last_name': 'Nom (A-Z)',
                '-student__last_name': 'Nom (Z-A)',
                'student__first_name': 'Prénom (A-Z)',
                '-student__first_name': 'Prénom (Z-A)',
                'student__matricule': 'Matricule (croissant)',
                '-student__matricule': 'Matricule (décroissant)',
                '-created_at': 'Plus récents',
                'created_at': 'Plus anciens'
            };

            // Show specific sort label or default to "Trier"
            sortText.textContent = sortLabels[currentSort] || 'Trier';
        } else if (sortText) {
            // Default state when no sort is applied
            sortText.textContent = 'Trier';
        }
    }

    // Update counts in status bar (called when new data is loaded)
    function updateStudentsStatusCounts(boysCount, girlsCount) {
        const totalCountEl = document.getElementById('total-count');
        const boysCountEl = document.getElementById('boys-count');
        const girlsCountEl = document.getElementById('girls-count');
        
        if (totalCountEl) totalCountEl.textContent = boysCount + girlsCount;
        if (boysCountEl) boysCountEl.textContent = boysCount;
        if (girlsCountEl) girlsCountEl.textContent = girlsCount;
    }

    // Make functions globally available
    window.updateStudentsStatusBar = updateStudentsStatusBar;
    window.updateStudentsStatusCounts = updateStudentsStatusCounts;

    // Initialize status bar when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', updateStudentsStatusBar);
    } else {
        updateStudentsStatusBar();
    }

    // Update status bar after HTMX swaps
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'content-area' || 
            event.detail.target.querySelector && event.detail.target.querySelector('#students-status-bar')) {
            setTimeout(() => updateStudentsStatusBar(), 100);
        }
    });
})();
</script>
